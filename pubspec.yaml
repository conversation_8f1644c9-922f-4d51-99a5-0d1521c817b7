name: kuwait_corners
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.2+3

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_bloc: ^9.0.0
  bloc: ^9.0.0
  firebase_core: ^2.32.0
  cloud_firestore: ^4.15.5
  firebase_auth: ^4.17.5
  firebase_storage: ^11.6.6
  firebase_messaging: ^14.7.16
  firebase_analytics: ^10.8.6
  firebase_database: ^10.4.6
  firebase_dynamic_links: ^5.4.8
  google_fonts: ^6.2.1
  go_router: ^14.7.1
  json_annotation: ^4.9.0
  flutter_screenutil: ^5.9.3

  cloud_functions: ^4.6.5
  flutter_dotenv: ^5.1.0
  equatable: ^2.0.7
  shared_preferences: ^2.2.2
  flutter_onboarding_slider: ^1.0.11
  flutter_localization: ^0.1.14
  image_picker: ^1.0.7
  shimmer: ^3.0.0
  dotted_border: ^2.1.0
  url_launcher: ^6.3.1
  image: ^4.1.3
  connectivity_plus: ^5.0.2
  cached_network_image: ^3.3.1
  flutter_cache_manager: ^3.3.1
  http: ^1.1.2
  google_sign_in: ^6.2.1
  flutter_local_notifications: ^17.2.3
  share_plus: ^7.2.1
  intl: ^0.19.0
  # Añadir soporte para emoji (commented out due to build issues)
  # emoji_picker_flutter: ^1.6.4
  emoji_regex: ^0.0.3

  # External services dependencies
  flutter_secure_storage: ^9.0.0
  http_parser: ^4.0.2
  path: ^1.9.0
  fl_chart: ^0.71.0
  image_cropper: ^9.1.0
  flutter_image_compress: ^2.3.0
  local_auth: ^2.1.8
  encrypt: ^5.0.3
  base32: ^2.1.3
  otp: ^3.1.4

  # Missing dependencies
  flutter_blurhash: ^0.8.2
  # Temporarily commenting out panorama and motion_sensors to fix build issues
  # panorama: ^0.4.1
  # motion_sensors: ^0.1.0
  device_info_plus: ^10.1.2
  battery_plus: ^5.0.3
  package_info_plus: ^8.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.5
  uuid: ^4.5.1
  step_progress_indicator: ^1.0.2
  flutter_staggered_animations: ^1.1.1
  google_maps_flutter: ^2.10.0
  reorderable_grid_view: ^2.2.8
  lottie: ^3.3.1
  percent_indicator: ^4.2.5
  provider: ^6.1.2
  timeago: ^3.7.1
  flutter_staggered_grid_view: ^0.7.0
  flutter_quill: ^9.5.0+1
  flutter_quill_extensions: ^9.5.0+1
  file_picker: ^5.5.0
  video_player: ^2.8.3
  syncfusion_flutter_xlsio: ^28.2.12

  # إضافة التبعيات المفقودة المطلوبة
  webview_flutter: ^4.4.2
  crypto: ^3.0.3
  qr_flutter: ^4.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: any
  json_serializable: ^6.6.1
  # إزالة التكرار - google_maps_flutter موجود في dependencies

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/icons/list.png
    - assets/banners/banner_app.jpg
    - assets/banners/banner_app1.jpg
    - assets/banners/banner_app2.jpg
    - assets/animations/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
